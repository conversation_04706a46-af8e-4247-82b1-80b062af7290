# GroupCell 组件

一个功能丰富的分组单元格组件，提供带圆角、阴影的卡片样式，包含多个可配置的 Cell 单元格。

## 功能特性

- ✅ **独立的 Cell 组件**：Cell 组件可以单独使用
- ✅ **左右插槽支持**：左侧和右侧都可以配置自定义插槽内容
- ✅ **图标支持**：左侧和右侧都可以指定图标
- ✅ **角标功能**：右侧可以添加数字或文本角标（类似未读消息）
- ✅ **圆角阴影**：GroupCell 样式带圆角、带阴影
- ✅ **边框控制**：可以配置最后一个 Cell 是否带下边框
- ✅ **响应式设计**：支持移动端适配
- ✅ **深色模式**：支持深色模式（可选）

## 组件结构

```
GroupCell/
├── index.vue          # GroupCell主组件
├── Cell/
│   └── index.vue      # Cell子组件
├── example.vue        # 使用示例
└── README.md          # 文档说明
```

## 基础用法

### 1. 导入组件

```typescript
import GroupCell, { type CellData } from "@/components/GroupCell/index.vue";
```

### 2. 基础使用

```vue
<template>
  <GroupCell :cell-list="cellList" @cell-click="handleCellClick" />
</template>

<script setup lang="ts">
import { ref } from "vue";
import GroupCell, { type CellData } from "@/components/GroupCell/index.vue";

const cellList = ref<CellData[]>([
  {
    title: "用户名",
    value: "张三",
  },
  {
    title: "手机号",
    value: "138****8888",
  },
]);

const handleCellClick = (payload: { cell: CellData; index: number }) => {
  console.log("Cell被点击:", payload);
};
</script>
```

## API 文档

### GroupCell Props

| 参数           | 说明                         | 类型         | 默认值  |
| -------------- | ---------------------------- | ------------ | ------- |
| cellList       | Cell 数据列表                | `CellData[]` | `[]`    |
| lastCellBorder | 最后一个 Cell 是否显示下边框 | `boolean`    | `false` |
| showShadow     | 是否显示阴影                 | `boolean`    | `true`  |
| borderRadius   | 圆角大小                     | `string`     | `'8px'` |

### GroupCell Events

| 事件名     | 说明              | 回调参数                            |
| ---------- | ----------------- | ----------------------------------- |
| cell-click | Cell 被点击时触发 | `{ cell: CellData, index: number }` |

### GroupCell Slots

| 插槽名        | 说明                              | 参数                                |
| ------------- | --------------------------------- | ----------------------------------- |
| left-{index}  | 自定义第{index}个 Cell 的左侧内容 | `{ cell: CellData, index: number }` |
| right-{index} | 自定义第{index}个 Cell 的右侧内容 | `{ cell: CellData, index: number }` |

### Cell Props

| 参数          | 说明                          | 类型                       | 默认值      |
| ------------- | ----------------------------- | -------------------------- | ----------- |
| leftIcon      | 左侧图标类名                  | `string`                   | `''`        |
| rightIcon     | 右侧图标类名                  | `string`                   | `''`        |
| title         | 标题文本（左侧默认内容）      | `string`                   | `''`        |
| value         | 值文本（右侧默认内容）        | `string`                   | `''`        |
| badge         | 角标内容（数字或文本）        | `string \| number \| null` | `null`      |
| badgeMaxValue | 角标最大值，超过时显示最大值+ | `number`                   | `99`        |
| badgeBgColor  | 角标背景色                    | `string`                   | `'#ff4757'` |
| showBorder    | 是否显示下边框                | `boolean`                  | `true`      |

### Cell Events

| 事件名 | 说明              | 回调参数 |
| ------ | ----------------- | -------- |
| click  | Cell 被点击时触发 | `Event`  |

### Cell Slots

| 插槽名 | 说明           |
| ------ | -------------- |
| left   | 自定义左侧内容 |
| right  | 自定义右侧内容 |

### CellData 接口

```typescript
interface CellData {
  /** 左侧图标类名 */
  leftIcon?: string;
  /** 右侧图标类名 */
  rightIcon?: string;
  /** 标题文本 */
  title?: string;
  /** 值文本 */
  value?: string;
  /** 角标内容 */
  badge?: string | number | null;
  /** 角标最大值，超过时显示最大值+ */
  badgeMaxValue?: number;
  /** 角标背景色 */
  badgeBgColor?: string;
  /** 自定义数据，用于点击事件回调 */
  data?: any;
}
```

## 使用示例

### 带角标的 Cell

```vue
<GroupCell
  :cell-list="[
    {
      title: '消息',
      value: '查看详情',
      badge: 5,
      rightIcon: 'el-icon-arrow-right',
    },
  ]"
/>
```

### 自定义角标样式

```vue
<GroupCell
  :cell-list="[
    {
      title: '消息',
      value: '查看详情',
      badge: 150,
      badgeMaxValue: 99, // 超过99显示99+
      badgeBgColor: '#409eff', // 自定义背景色
      rightIcon: 'el-icon-arrow-right',
    },
    {
      title: '通知',
      value: '查看详情',
      badge: 'New', // 文本角标正常显示
      badgeBgColor: '#67c23a',
      rightIcon: 'el-icon-arrow-right',
    },
  ]"
/>
```

### 带图标的 Cell

```vue
<GroupCell
  :cell-list="[
    {
      leftIcon: 'el-icon-setting',
      title: '设置',
      value: '系统设置',
      rightIcon: 'el-icon-arrow-right',
    },
  ]"
/>
```

### 自定义插槽内容

```vue
<GroupCell :cell-list="cellList">
  <template v-slot:left-0="{ cell, index }">
    <div class="custom-content">
      <i class="el-icon-user"></i>
      <span>{{ cell.title }}</span>
    </div>
  </template>
</GroupCell>
```

### 配置样式

```vue
<GroupCell
  :cell-list="cellList"
  :last-cell-border="true"
  :show-shadow="false"
  border-radius="16px"
/>
```

## 样式定制

组件使用 SCSS 编写，支持以下 CSS 变量定制：

- 圆角大小：通过 `border-radius` prop 控制
- 阴影效果：通过 `show-shadow` prop 控制
- 边框显示：通过 `last-cell-border` prop 控制

## 注意事项

1. 组件基于 Vue 2.7 + TypeScript + Element UI 开发
2. 图标使用 Element UI 的图标库，确保项目中已引入 Element UI
3. 角标支持数字和文本，超过 99 的数字建议显示为"99+"
4. 插槽名称格式为 `left-{index}` 和 `right-{index}`，其中 index 从 0 开始
5. 组件支持响应式设计，在移动端会自动调整样式

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 55
- Safari >= 12
- Edge >= 79
- iOS Safari >= 12
- Android Chrome >= 60
