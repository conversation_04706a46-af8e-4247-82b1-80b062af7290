<template>
  <div class="example-container">
    <h2>GroupCell 组件使用示例</h2>

    <!-- 基础用法 -->
    <h3>1. 基础用法</h3>
    <GroupCell :cell-list="basicCellList" @cell-click="handleCellClick" />

    <!-- 带角标的用法 -->
    <h3>2. 带角标</h3>
    <GroupCell :cell-list="badgeCellList" @cell-click="handleCellClick" />

    <!-- 自定义角标样式 -->
    <h3>3. 自定义角标样式</h3>
    <GroupCell :cell-list="customBadgeCellList" @cell-click="handleCellClick" />

    <!-- 自定义图标 -->
    <h3>4. 自定义图标</h3>
    <GroupCell :cell-list="iconCellList" @cell-click="handleCellClick" />

    <!-- 最后一个Cell带边框 -->
    <h3>5. 最后一个Cell带边框</h3>
    <GroupCell
      :cell-list="basicCellList"
      :last-cell-border="true"
      @cell-click="handleCellClick"
    />

    <!-- 无阴影样式 -->
    <h3>6. 无阴影样式</h3>
    <GroupCell
      :cell-list="basicCellList"
      :show-shadow="false"
      @cell-click="handleCellClick"
    />

    <!-- 自定义圆角 -->
    <h3>6. 自定义圆角</h3>
    <GroupCell
      :cell-list="basicCellList"
      border-radius="16px"
      @cell-click="handleCellClick"
    />

    <!-- 使用插槽自定义内容 -->
    <h3>7. 使用插槽自定义内容</h3>
    <GroupCell :cell-list="slotCellList" @cell-click="handleCellClick">
      <!-- 自定义第一个Cell的左侧内容 -->
      <template v-slot:left-0="{ cell, index }">
        <div class="custom-left">
          <i class="el-icon-user" style="color: #409eff; margin-right: 8px"></i>
          <span style="font-weight: bold">{{ cell.title }}</span>
        </div>
      </template>

      <!-- 自定义第一个Cell的右侧内容 -->
      <template v-slot:right-0="{ cell, index }">
        <div class="custom-right">
          <span style="color: #67c23a">{{ cell.value }}</span>
          <i class="el-icon-check" style="color: #67c23a; margin-left: 8px"></i>
        </div>
      </template>
    </GroupCell>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GroupCell from "./index.vue";
import { CellData } from "./types";

// 基础Cell数据
const basicCellList = ref<CellData[]>([
  {
    title: "用户名",
    value: "张三",
    data: { userId: 1 },
  },
  {
    title: "手机号",
    value: "138****8888",
    data: { type: "phone" },
  },
  {
    title: "邮箱",
    value: "<EMAIL>",
    data: { type: "email" },
  },
]);

// 带角标的Cell数据
const badgeCellList = ref<CellData[]>([
  {
    title: "消息",
    value: "查看详情",
    badge: 5,
    rightIcon: "el-icon-arrow-right",
    data: { type: "message" },
  },
  {
    title: "通知",
    value: "查看详情",
    badge: 99,
    rightIcon: "el-icon-arrow-right",
    data: { type: "notification" },
  },
  {
    title: "待办事项",
    value: "查看详情",
    badge: "New",
    rightIcon: "el-icon-arrow-right",
    data: { type: "todo" },
  },
]);

// 自定义角标样式的Cell数据
const customBadgeCellList = ref<CellData[]>([
  {
    title: "消息",
    value: "查看详情",
    badge: 150,
    badgeMaxValue: 99, // 超过99显示99+
    badgeBgColor: "#409eff", // 蓝色背景
    rightIcon: "el-icon-arrow-right",
    data: { type: "message" },
  },
  {
    title: "通知",
    value: "查看详情",
    badge: "New",
    badgeBgColor: "#67c23a", // 绿色背景
    rightIcon: "el-icon-arrow-right",
    data: { type: "notification" },
  },
  {
    title: "警告",
    value: "查看详情",
    badge: 999,
    badgeMaxValue: 500, // 超过500显示500+
    badgeBgColor: "#e6a23c", // 橙色背景
    rightIcon: "el-icon-arrow-right",
    data: { type: "warning" },
  },
  {
    title: "错误",
    value: "查看详情",
    badge: 3,
    badgeBgColor: "#f56c6c", // 红色背景
    rightIcon: "el-icon-arrow-right",
    data: { type: "error" },
  },
]);

// 带图标的Cell数据
const iconCellList = ref<CellData[]>([
  {
    leftIcon: "el-icon-setting",
    title: "设置",
    value: "系统设置",
    rightIcon: "el-icon-arrow-right",
    data: { type: "setting" },
  },
  {
    leftIcon: "el-icon-help",
    title: "帮助",
    value: "使用帮助",
    rightIcon: "el-icon-arrow-right",
    data: { type: "help" },
  },
  {
    leftIcon: "el-icon-info",
    title: "关于",
    value: "关于我们",
    rightIcon: "el-icon-arrow-right",
    data: { type: "about" },
  },
]);

// 用于插槽示例的Cell数据
const slotCellList = ref<CellData[]>([
  {
    title: "自定义用户",
    value: "在线",
    data: { status: "online" },
  },
  {
    title: "普通项目",
    value: "正常显示",
    data: { type: "normal" },
  },
]);

/**
 * Cell点击事件处理
 * @param payload 点击事件数据
 */
const handleCellClick = (payload: { cell: CellData; index: number }) => {
  console.log("Cell被点击:", payload);

  // 可以根据不同的数据类型执行不同的操作
  const { cell, index } = payload;

  if (cell.data?.type === "message") {
    console.log("跳转到消息页面");
  } else if (cell.data?.type === "setting") {
    console.log("打开设置页面");
  } else {
    console.log(`点击了第${index + 1}个Cell:`, cell.title);
  }
};
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #666;
    margin: 30px 0 15px 0;
    font-size: 16px;
    border-left: 4px solid #409eff;
    padding-left: 12px;
  }
}

.custom-left {
  display: flex;
  align-items: center;
}

.custom-right {
  display: flex;
  align-items: center;
}

// 移动端适配
@media (max-width: 768px) {
  .example-container {
    padding: 15px;
  }

  h2 {
    font-size: 20px;
  }

  h3 {
    font-size: 14px;
  }
}
</style>
