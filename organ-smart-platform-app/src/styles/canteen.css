:root {
  --color-primary: #409eff;
  --color-primary-plain-bg: #f0f8ff;

  --color-primary-light: #e1e8f0;
  --tabbar-height: 60px;
  --header-height: 48px;
}

.canteen-btn {
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}
.canteen-btn:hover {
  opacity: 0.8;
}
.canteen-btn.plain {
  color: var(--color-primary);
  background: var(--color-primary-plain-bg);
  border: 1px solid var(--color-primary);
}
.canteen-btn.rounded {
  border-radius: 9999px;
}

.btn-secondary {
  background: #ffffff;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-primary {
  background: var(--color-primary);
  color: #ffffff;
}

.btn-normal {
  padding: 16px;
  font-size: 18px;
}

.canteen-tag {
  color: #ffffff;
  background: var(--color-primary);
  padding: 6px 12px;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 1;
}
.canteen-tag.plain {
  color: var(--color-primary);
  background: var(--color-primary-plain-bg);
}
.canteen-content-pd {
  padding: 20px 16px;
}
.canteen-card {
  border-radius: 8px;
  padding: 20px;
  background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;
}
