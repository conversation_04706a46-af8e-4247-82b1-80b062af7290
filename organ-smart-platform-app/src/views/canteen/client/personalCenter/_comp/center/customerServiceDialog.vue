<template>
  <div v-if="visible" class="user-info-overlay" @click="handleOverlayClick">
    <div class="user-info-dialog" @click.stop>
      <!-- 客服列表 -->
      <div class="info-content">
        <!-- 顶部 渐变背景+图片 -->
        <div class="header-section">
          <span class="info-title">联系客服</span>
        </div>

        <!-- 客服列表卡片 -->
        <div class="user-info-card">
          <div class="card-content">
            <!-- 使用GroupCell展示 -->
            <GroupCell
              :cell-list="groupCellList"
              @cell-click="handleContactCustomerService"
            />
            <!-- <div class="info-item" v-for="info in customerServiceList">
              <span class="label">{{ info.name }}</span>
              <span class="value">{{ info.phone }}</span>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import GroupCell from "@/components/GroupCell/index.vue";
import { ICustomerService } from "@/api/canteen/client/types/systemConfig";
import { copyText } from "@/utils/copy";
import { useToast } from "@/components/Toast";
import { CellClickPayload } from "@/components/GroupCell/types";

/**
 * 客服列表弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 客服列表数据 */
  customerServiceList: ICustomerService[];
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
}

/** toast hook */
const toastHook = useToast();

const props = withDefaults(defineProps<IProps>(), {
  value: false,
  customerServiceList: () => [],
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/** 转换为GroupCell需要的格式 */
const groupCellList = computed(() => {
  return props.customerServiceList.map((item) => {
    return {
      title: `${item.name}`,
      value: item.phone,
      leftIcon: "el-icon-phone",
      data: item,
    };
  });
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};

/** 点击联系客服 */
const handleContactCustomerService = async (
  payload: CellClickPayload<ICustomerService>
) => {
  // H5 海政通内嵌 唤起拨号（无法唤起拨号时复制联系电话）
  try {
    // APP 唤起拨号
    // TODO: 待测试!!!!
    window.location.href = `tel:${payload.cell.data.phone}`;
  } catch (error) {
    // 无法唤起拨号时复制联系电话
    const res = await copyText(payload.cell.data.phone);
    if (res) {
      toastHook.success("联系方式已复制", {
        showIcon: false,
      });
    }
  }
};
</script>

<style scoped lang="scss">
.user-info-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .user-info-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .info-content {
      overflow: hidden;
      background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);

      .header-section {
        // height: 70px;
        padding-top: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        padding-left: 20px;

        .info-title {
          font-size: 20px;
          font-weight: 600;
          color: #383838;
          opacity: 1;
        }
      }

      // 客服列表卡片
      .user-info-card {
        width: 100%;
        z-index: 2;
        border-radius: 4px;

        .info-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 0;
          font-size: 16px;
          border-bottom: 1px solid #f0f8ff;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            flex-shrink: 0;
            width: 80px;
            color: #808080;
          }

          .value {
            text-align: right;
            color: #1d1e20;

            &.canteen-tag {
              color: var(--color-primary);
            }
          }
        }
      }
    }

    .close-btn-wrapper {
      text-align: center;
      padding: 20px 0 0;
    }
  }
}
</style>
